{"name": "meal-composer", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"next": "15.1.0", "puppeteer": "^24.10.0", "puppeteer-cluster": "^0.24.0", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "postcss": "^8", "tailwindcss": "^3.4.1", "eslint": "^9", "eslint-config-next": "15.1.0", "@eslint/eslintrc": "^3"}}