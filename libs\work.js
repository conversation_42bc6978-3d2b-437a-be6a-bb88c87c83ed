import {Worker} from "bullmq";
import IORedis from "ioredis";
import puppeteer from "puppeteer";
import { viewports } from "./config/viewports.js";
import { runChecksForDevice } from "./checks/runChecksForDevice.js";

const connection = new IORedis({
  host: 'redis',
  port: 6379,
  maxRetriesPerRequest: null,
});

const worker = new Worker(
  'visibility-check',
  async (job) => {
    const { url, articleId, layers } = job.data;
    const activeLayers = layers || ['placement', 'visibility'];

    console.log(`📥 Start analysis: ${url} with layers [${activeLayers.join(', ')}]`);

    const startTime = Date.now();

    const browser = await puppeteer.launch({
      executablePath: process.env.PUPPETEER_EXECUTABLE_PATH || undefined,
      headless: 'new', // Use 'new' for headless mode
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });

    try {
      const consentState = { handled: false }; // Flag to track if consent has been handled.

      const deviceResults = [];
      // Run checks sequentially to handle consent state correctly
      for (const [deviceName, config] of Object.entries(viewports)) {
          const result = await runChecksForDevice(browser, url, deviceName, config, activeLayers, consentState);
          deviceResults.push(result);
      }

      const finalIssues = {};
      let totalIssues = 0;
      const deviceErrors = [];

      for (const result of deviceResults) {
          if (result.error) {
              deviceErrors.push({ device: result.deviceName, error: result.error });
          }
          totalIssues += result.issueCount;

          for (const layer of ['placement', 'visibility']) {
              if (result.issues[layer]) {
                  if (!finalIssues[layer]) {
                      // Initialize with all possible keys
                      finalIssues[layer] = { desktop: [], tablet: [], mobile: [] };
                  }
                  const responsiveKey = result.responsiveKey;
                  if (result.issues[layer][responsiveKey]) {
                      // Ensure the key exists before pushing
                      if (!finalIssues[layer][responsiveKey]) {
                          finalIssues[layer][responsiveKey] = [];
                      }
                      finalIssues[layer][responsiveKey].push(...result.issues[layer][responsiveKey]);
                  }
              }
          }
      }

      const endTime = Date.now();
      const durationMs = endTime - startTime;
      
      let finalResult;

      if (totalIssues === 0 && deviceErrors.length === 0) {
          finalResult = {
              id: articleId.toString(),
              success: true,
              url,
              checkedLayers: activeLayers,
              timestamp: new Date().toISOString(),
              processingTimeMs: durationMs
          };
      } else {
          finalResult = {
              id: articleId.toString(),
              success: false,
              totalIssues: totalIssues, // Added from test logic
              url,
              checkedLayers: activeLayers,
              issues: finalIssues,
              errors: deviceErrors,
              timestamp: new Date().toISOString(),
              processingTimeMs: durationMs
          };
      }

      console.log("final result 📈 :" ,JSON.stringify(finalResult, null, 2))
    
      return finalResult

    } catch (e) {
      console.error(`❌ Critical error during job execution for ${url}:`, e.message);
      return {
          id: articleId.toString(),
          url,
          error: e.message,
          timestamp: new Date().toISOString(),
      }
    } finally {
      await browser.close();
    }
  },
  { connection }
  
);

worker.on('completed', (job, result) => {
  console.log(`🎉 Task completed: ${job.id}`);
});

worker.on('failed', (job, err) => {
  console.error(`❌ Job failed: ${job.id} - ${err.message}`);
});
